# Operating System Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Editor Files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Analytics API keys (if using files)
analytics-keys.json
google-analytics-key.json

# Build directories (if you add build tools later)
dist/
build/
public/

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/

# Temporary folders
tmp/
temp/

# Cache files
.cache/

# Service worker debug files
sw-debug.log
