/**
 * SpinChoice PWA - Random Decision Maker
 * Main application logic for handling user interactions and randomization
 */

class SpinChoice {
    constructor() {
        this.isSpinning = false;
        this.defaultOptions = ['Yes', 'No'];
        this.currentOptions = [...this.defaultOptions];
        
        this.initializeElements();
        this.bindEvents();
        this.updateOptionsPreview();
        this.registerServiceWorker();
    }

    /**
     * Initialize DOM elements
     */
    initializeElements() {
        this.optionsInput = document.getElementById('custom-options');
        this.spinBtn = document.getElementById('spin-btn');
        this.resultSection = document.getElementById('result-section');
        this.resultText = document.getElementById('result-text');
        this.optionsList = document.getElementById('options-list');
        this.loadingOverlay = document.getElementById('loading-overlay');
        this.shareBtn = document.getElementById('share-btn');
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Spin button click event
        this.spinBtn.addEventListener('click', () => this.handleSpin());
        
        // Share button click event
        if (this.shareBtn) {
            this.shareBtn.addEventListener('click', () => this.handleShare());
        }
        
        // Options input change event (with debouncing)
        let inputTimeout;
        if (this.optionsInput) {
            this.optionsInput.addEventListener('input', () => {
                clearTimeout(inputTimeout);
                inputTimeout = setTimeout(() => {
                    this.updateOptions();
                }, 300);
            });
        }
        
        // Category button click events
        const categoryButtons = document.querySelectorAll('.category-btn');
        categoryButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleCategorySelect(e));
        });
        
        // Custom toggle button
        const customToggleBtn = document.getElementById('custom-toggle-btn');
        const customInputSection = document.getElementById('custom-input-section');
        if (customToggleBtn && customInputSection) {
            customToggleBtn.addEventListener('click', () => {
                const isHidden = customInputSection.style.display === 'none';
                customInputSection.style.display = isHidden ? 'block' : 'none';
                customToggleBtn.innerHTML = isHidden 
                    ? '<span class="toggle-icon">📝</span>Hide custom options'
                    : '<span class="toggle-icon">⚙️</span>Or create your own options';
                
                if (isHidden) {
                    // Clear category selections when opening custom input
                    this.clearCategorySelections();
                    this.optionsInput.focus();
                }
            });
        }

            // Handle Enter key in textarea (optional spin trigger)
            this.optionsInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                    e.preventDefault();
                    this.handleSpin();
                }
            });
        }

        // Prevent form submission if wrapped in a form
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && e.target !== this.optionsInput) {
                e.preventDefault();
            }
        });
    }

    /**
     * Handle category selection
     */
    handleCategorySelect(event) {
        const categoryBtn = event.currentTarget;
        const options = categoryBtn.dataset.options;
        
        // Clear previous selections
        this.clearCategorySelections();
        
        // Mark this category as selected
        categoryBtn.classList.add('selected');
        
        // Update options
        this.setOptionsFromString(options);
        
        // Hide custom input section
        const customInputSection = document.getElementById('custom-input-section');
        if (customInputSection) {
            customInputSection.style.display = 'none';
        }
        
        // Update toggle button text
        const customToggleBtn = document.getElementById('custom-toggle-btn');
        if (customToggleBtn) {
            customToggleBtn.innerHTML = '<span class="toggle-icon">⚙️</span>Or create your own options';
        }
    }

    /**
     * Clear all category selections
     */
    clearCategorySelections() {
        const categoryButtons = document.querySelectorAll('.category-btn');
        categoryButtons.forEach(btn => btn.classList.remove('selected'));
    }

    /**
     * Set options from comma-separated string
     */
    setOptionsFromString(optionsString) {
        if (!optionsString || optionsString.trim() === '') {
            this.currentOptions = [...this.defaultOptions];
        } else {
            // Split by comma or newline and clean up
            const options = optionsString
                .split(/[,\n]/)
                .map(option => option.trim())
                .filter(option => option.length > 0);
            
            this.currentOptions = options.length > 0 ? options : [...this.defaultOptions];
        }
        
        // Update the options input if it exists
        if (this.optionsInput) {
            this.optionsInput.value = this.currentOptions.join(', ');
        }
        
        this.updateOptionsPreview();
        this.updateSpinButton();
    }

    /**
     * Update options based on user input
     */
    updateOptions() {
        const inputValue = this.optionsInput ? this.optionsInput.value.trim() : '';
        
        if (!inputValue) {
            // Use default options if input is empty
            this.currentOptions = [...this.defaultOptions];
        } else {
            // Parse options - support both comma-separated and line-separated
            let options = [];
            
            // First try line-separated
            if (inputValue.includes('\n')) {
                options = inputValue.split('\n')
                    .map(opt => opt.trim())
                    .filter(opt => opt.length > 0);
            } else {
                // Then try comma-separated
                options = inputValue.split(',')
                    .map(opt => opt.trim())
                    .filter(opt => opt.length > 0);
            }
            
            // Fallback to single option if no separators found
            if (options.length === 0 && inputValue.length > 0) {
                options = [inputValue];
            }
            
            // Use parsed options or default if empty
            this.currentOptions = options.length > 0 ? options : [...this.defaultOptions];
        }
        
        this.updateOptionsPreview();
        this.updateSpinButton();
        this.hideResult();
    }

    /**
     * Set preset options for landing pages
     */
    setPresetOptions(options) {
        this.currentOptions = [...options];
        if (this.optionsInput) {
            this.optionsInput.value = options.join(', ');
        }
        this.updateOptionsPreview();
        this.updateSpinButton();
    }

    /**
     * Update spin button state based on available options
     */
    updateSpinButton() {
        if (this.currentOptions.length === 0) {
            this.spinBtn.disabled = true;
            this.spinBtn.querySelector('.spin-text').textContent = '🎲 ADD OPTIONS TO SPIN';
        } else {
            this.spinBtn.disabled = false;
            const buttonText = this.currentOptions.length === 2 && 
                              this.currentOptions.includes('Yes') && 
                              this.currentOptions.includes('No') 
                              ? '🎲 SPIN FOR YES OR NO' 
                              : '🎲 SPIN';
            this.spinBtn.querySelector('.spin-text').textContent = buttonText;
        }
    }

    /**
     * Update the options preview display
     */
    updateOptionsPreview() {
        if (this.currentOptions.length <= 6) {
            // Show all options if 6 or fewer
            this.optionsList.textContent = this.currentOptions.join(', ');
        } else {
            // Show first 5 options and count for more
            const preview = this.currentOptions.slice(0, 5).join(', ');
            const remaining = this.currentOptions.length - 5;
            this.optionsList.textContent = `${preview}... (+${remaining} more)`;
        }
    }

    /**
     * Handle spin button click
     */
    async handleSpin() {
        if (this.isSpinning || this.currentOptions.length === 0) {
            return;
        }

        this.isSpinning = true;
        this.hideResult();
        await this.animateSpin();
        
        const selectedOption = this.getRandomOption();
        this.showResult(selectedOption);
        
        this.isSpinning = false;
    }

    /**
     * Animate the spin button
     */
    async animateSpin() {
        return new Promise((resolve) => {
            // Disable button and add spinning class
            this.spinBtn.disabled = true;
            this.spinBtn.classList.add('spinning');
            
            // Generate random spin duration between 1-2 seconds
            const spinDuration = Math.random() * 1000 + 1000;
            
            setTimeout(() => {
                this.spinBtn.disabled = false;
                this.spinBtn.classList.remove('spinning');
                resolve();
            }, spinDuration);
        });
    }

    /**
     * Get a random option from current options
     */
    getRandomOption() {
        if (this.currentOptions.length === 0) {
            return this.defaultOptions[0];
        }
        
        const randomIndex = Math.floor(Math.random() * this.currentOptions.length);
        return this.currentOptions[randomIndex];
    }

    /**
     * Show the result with animation
     */
    showResult(result) {
        this.resultText.textContent = result;
        this.resultSection.classList.remove('hidden');
        
        // Add some haptic feedback on mobile devices
        if ('vibrate' in navigator) {
            navigator.vibrate(200);
        }
        
        // Scroll result into view smoothly
        setTimeout(() => {
            this.resultSection.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'center' 
            });
        }, 100);
        
        // Log for analytics (can be removed in production)
        console.log(`SpinChoice result: ${result} from [${this.currentOptions.join(', ')}]`);
    }

    /**
     * Handle share button click
     */
    async handleShare() {
        const result = this.resultText.textContent;
        if (!result) return;

        const shareText = `🎯 The wheel decided: ${result}`;
        const shareUrl = window.location.href;

        // Try Web Share API first (mobile-friendly)
        if ('share' in navigator) {
            try {
                await navigator.share({
                    title: 'SpinChoice Result',
                    text: shareText,
                    url: shareUrl
                });
                console.log('Shared successfully via Web Share API');
            } catch (error) {
                if (error.name !== 'AbortError') {
                    console.log('Web Share failed, trying clipboard:', error);
                    this.fallbackShare(shareText);
                }
            }
        } else {
            // Fallback for desktop browsers
            this.fallbackShare(shareText);
        }
    }

    /**
     * Fallback share method - copy to clipboard or show alert
     */
    async fallbackShare(text) {
        try {
            if ('clipboard' in navigator) {
                await navigator.clipboard.writeText(text);
                this.showShareFeedback('Result copied to clipboard! 📋');
            } else {
                throw new Error('Clipboard not available');
            }
        } catch (error) {
            // Final fallback - show alert
            alert(`Copy this result: ${text}`);
        }
    }

    /**
     * Hide the result section
     */
    hideResult() {
        this.resultSection.classList.add('hidden');
    }

    /**
     * Register service worker for PWA functionality
     */
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                await navigator.serviceWorker.register('./service-worker.js');
                console.log('Service Worker registered successfully');
            } catch (error) {
                console.log('Service Worker registration failed:', error);
            }
        }
    }
}

/**
 * Utility functions for additional features
 */
class SpinChoiceUtils {
    /**
     * Save current options to localStorage
     */
    static saveOptions(options) {
        try {
            localStorage.setItem('spinChoice_options', JSON.stringify(options));
        } catch (error) {
            console.warn('Could not save options to localStorage:', error);
        }
    }

    /**
     * Load options from localStorage
     */
    static loadOptions() {
        try {
            const saved = localStorage.getItem('spinChoice_options');
            return saved ? JSON.parse(saved) : null;
        } catch (error) {
            console.warn('Could not load options from localStorage:', error);
            return null;
        }
    }

    /**
     * Clear saved options
     */
    static clearSavedOptions() {
        try {
            localStorage.removeItem('spinChoice_options');
        } catch (error) {
            console.warn('Could not clear saved options:', error);
        }
    }

    /**
     * Export options as text file
     */
    static exportOptions(options) {
        const text = options.join('\n');
        const blob = new Blob([text], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'spinChoice_options.txt';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    /**
     * Share result using Web Share API (if available)
     */
    static async shareResult(result, options) {
        if ('share' in navigator) {
            try {
                await navigator.share({
                    title: 'SpinChoice Result',
                    text: `🎯 SpinChoice picked: "${result}" from [${options.join(', ')}]`,
                    url: window.location.href
                });
            } catch (error) {
                // User cancelled or error occurred
                console.log('Share cancelled or failed:', error);
            }
        } else {
            // Fallback: copy to clipboard
            try {
                await navigator.clipboard.writeText(`🎯 SpinChoice picked: "${result}"`);
                // Could show a toast notification here
                console.log('Result copied to clipboard');
            } catch (error) {
                console.warn('Could not copy to clipboard:', error);
            }
        }
    }
}

/**
 * PWA Installation Helper
 */
class PWAInstaller {
    constructor() {
        this.deferredPrompt = null;
        this.installBanner = document.getElementById('install-banner');
        this.installBtn = document.getElementById('install-btn');
        this.dismissBtn = document.getElementById('dismiss-install');
        this.bindEvents();
    }

    bindEvents() {
        // Listen for the beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallBanner();
        });

        // Listen for the app installed event
        window.addEventListener('appinstalled', () => {
            console.log('PWA was installed');
            this.hideInstallBanner();
            this.showInstallSuccess();
        });

        // Install button click
        if (this.installBtn) {
            this.installBtn.addEventListener('click', () => {
                this.promptInstall();
            });
        }

        // Dismiss button click
        if (this.dismissBtn) {
            this.dismissBtn.addEventListener('click', () => {
                this.hideInstallBanner();
                this.saveDismissPreference();
            });
        }
    }

    showInstallBanner() {
        // Check if user previously dismissed
        if (localStorage.getItem('installBannerDismissed')) {
            return;
        }

        if (this.installBanner) {
            this.installBanner.classList.remove('hidden');
            console.log('Install banner shown');
        }
    }

    hideInstallBanner() {
        if (this.installBanner) {
            this.installBanner.classList.add('hidden');
        }
    }

    saveDismissPreference() {
        localStorage.setItem('installBannerDismissed', 'true');
    }

    async promptInstall() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            const { outcome } = await this.deferredPrompt.userChoice;
            console.log(`User ${outcome} the install prompt`);
            
            if (outcome === 'accepted') {
                this.hideInstallBanner();
            }
            
            this.deferredPrompt = null;
        }
    }

    showInstallSuccess() {
        // Show success message
        const message = 'SpinChoice installed successfully! 🎉';
        if (window.spinChoiceApp && window.spinChoiceApp.showShareFeedback) {
            window.spinChoiceApp.showShareFeedback(message);
        }
    }
}

/**
 * Initialize the application when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', () => {
    // Initialize main app
    window.spinChoiceApp = new SpinChoice();
    
    // Initialize PWA installer
    window.pwaInstaller = new PWAInstaller();
    
    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Spacebar to spin
        if (e.code === 'Space' && e.target !== window.spinChoiceApp.optionsInput) {
            e.preventDefault();
            window.spinChoiceApp.handleSpin();
        }
        
        // Ctrl+R to reset input
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            window.spinChoiceApp.optionsInput.value = 'Yes, No';
            window.spinChoiceApp.updateOptions();
        }
    });
    
    console.log('🎯 SpinChoice PWA initialized successfully!');
});

/**
 * Handle offline/online status
 */
window.addEventListener('online', () => {
    console.log('App is online');
});

window.addEventListener('offline', () => {
    console.log('App is offline');
});
