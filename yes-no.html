<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yes or No Decision Maker | SpinChoice</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Quick Yes or No decisions made easy! Use SpinChoice's Yes/No wheel to make instant binary decisions. Perfect for quick choices and eliminating indecision.">
    <meta name="keywords" content="yes no decision, binary choice, yes or no wheel, quick decision, coin flip, random yes no">
    <meta name="author" content="SpinChoice">
    <meta property="og:title" content="Yes or No Decision Maker | SpinChoice">
    <meta property="og:description" content="Can't decide? Let SpinChoice pick Yes or No for you! Quick, simple, and effective binary decision making.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="">
    <meta property="og:image" content="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><circle cx='256' cy='256' r='240' fill='%234CAF50'/><text x='256' y='320' text-anchor='middle' fill='white' font-size='200' font-family='Arial'>🎯</text></svg>">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Yes or No Decision Maker | SpinChoice">
    <meta name="twitter:description" content="Can't decide? Let SpinChoice pick Yes or No for you! Quick, simple, and effective binary decision making.">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4CAF50">
    <meta name="description" content="SpinChoice - A simple random decision maker PWA">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Apple Touch Icon -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SpinChoice">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='45' fill='%234CAF50'/><text x='50' y='58' text-anchor='middle' fill='white' font-size='24' font-family='Arial'>🎯</text></svg>">
    
    <!-- Styles -->
    <link rel="stylesheet" href="style.css">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "SpinChoice Yes/No",
      "description": "Quick Yes or No decisions made easy with SpinChoice's binary decision wheel",
      "applicationCategory": "Utility",
      "operatingSystem": "Any",
      "offers": {
        "@type": "Offer",
        "price": "0"
      },
      "featureList": [
        "Yes/No decision making",
        "Instant binary choices",
        "Mobile-friendly design",
        "Offline functionality"
      ]
    }
    </script>
</head>
<body>
    <div class="container">
        <header>
            <h1 class="app-title">✅ Yes or No?</h1>
            <p class="subtitle">Let the wheel decide your binary choices</p>
            <nav class="page-nav">
                <a href="index.html" class="nav-link">All Options</a>
                <a href="what-to-eat.html" class="nav-link">Food Choices</a>
                <a href="random-choice.html" class="nav-link">Custom Wheel</a>
            </nav>
        </header>

        <main class="main-content">
            <!-- Preset Info -->
            <div class="preset-info">
                <h2 class="preset-title">🎯 Binary Decision Wheel</h2>
                <p class="preset-description">
                    Perfect for quick Yes/No decisions! Whether you're deciding to go out, 
                    make a purchase, or take a risk - let SpinChoice eliminate the indecision.
                </p>
            </div>

            <!-- Custom Options Input (Hidden for preset pages) -->
            <div class="input-section" style="display: none;">
                <label for="custom-options" class="input-label">
                    Enter your options (one per line or comma-separated):
                </label>
                <textarea 
                    id="custom-options" 
                    class="options-input" 
                    placeholder="Yes, No"
                    rows="4"
                >Yes, No</textarea>
                <p class="input-help">Leave blank to use default Yes/No options</p>
            </div>

            <!-- Spin Button -->
            <div class="spin-section">
                <button id="spin-btn" class="spin-button">
                    <span class="spin-text">🎲 SPIN FOR YES OR NO</span>
                </button>
            </div>

            <!-- Result Display -->
            <div id="result-section" class="result-section hidden">
                <div class="result-label">The answer is:</div>
                <div id="result-text" class="result-text"></div>
                <button id="share-btn" class="share-button">
                    <span class="share-icon">📤</span>
                    Share Result
                </button>
            </div>

            <!-- Options Preview -->
            <div class="options-preview">
                <p class="preview-label">Your choices:</p>
                <div id="options-list" class="options-list">Yes, No</div>
            </div>

            <!-- Usage Examples -->
            <div class="usage-examples">
                <h3 class="examples-title">Perfect for deciding:</h3>
                <ul class="examples-list">
                    <li>🚶 Should I go out tonight?</li>
                    <li>💰 Should I make this purchase?</li>
                    <li>🎬 Should I watch a movie?</li>
                    <li>☕ Should I have another coffee?</li>
                    <li>🏃 Should I exercise today?</li>
                </ul>
            </div>
        </main>

        <footer>
            <p class="footer-text">🎯 SpinChoice v1.0 - Make decisions easier</p>
            <!-- Install App Banner -->
            <div id="install-banner" class="install-banner hidden">
                <div class="install-content">
                    <span class="install-icon">📱</span>
                    <span class="install-text">Install SpinChoice for quick access!</span>
                    <button id="install-btn" class="install-button">Install</button>
                    <button id="dismiss-install" class="dismiss-button">×</button>
                </div>
            </div>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="spinner"></div>
    </div>

    <!-- Scripts -->
    <script src="app.js"></script>
    <script>
        // Set preset options for this page
        document.addEventListener('DOMContentLoaded', function() {
            if (window.spinChoiceApp) {
                window.spinChoiceApp.setPresetOptions(['Yes', 'No']);
            }
        });
    </script>
</body>
</html>
