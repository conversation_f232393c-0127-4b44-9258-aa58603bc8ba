/* SpinChoice PWA Styles */

/* CSS Variables for consistent theming */
:root {
    --primary-color: #4CAF50;
    --primary-dark: #45a049;
    --secondary-color: #f1f1f1;
    --text-color: #333;
    --text-light: #666;
    --background: #fafafa;
    --white: #ffffff;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 6px 12px rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: var(--background);
    color: var(--text-color);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Container and layout */
.container {
    max-width: 500px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    animation: fadeInUp 0.8s ease-out;
}

.app-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 8px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
    font-weight: 300;
}

/* Main content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Input section */
.input-section {
    background: var(--white);
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.input-label {
    display: block;
    margin-bottom: 12px;
    font-weight: 600;
    color: var(--text-color);
    font-size: 1rem;
}

.options-input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    min-height: 100px;
    transition: var(--transition);
    background: #fafafa;
}

.options-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
    background: var(--white);
}

.input-help {
    margin-top: 8px;
    font-size: 0.9rem;
    color: var(--text-light);
    font-style: italic;
}

/* Spin section */
.spin-section {
    text-align: center;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.spin-button {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    border: none;
    padding: 20px 40px;
    font-size: 1.4rem;
    font-weight: 700;
    border-radius: 50px;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow);
    min-width: 200px;
    position: relative;
    overflow: hidden;
}

.spin-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.spin-button:active {
    transform: translateY(0);
    box-shadow: var(--shadow);
}

.spin-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.spin-text {
    display: inline-block;
    transition: var(--transition);
}

/* Spinning animation */
.spin-button.spinning .spin-text {
    animation: spinText 1s ease-in-out;
}

@keyframes spinText {
    0%, 100% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(90deg) scale(0.8); }
    50% { transform: rotate(180deg) scale(1.1); }
    75% { transform: rotate(270deg) scale(0.9); }
}

/* Result section */
.result-section {
    background: var(--white);
    padding: 30px;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow);
    border: 3px solid var(--primary-color);
    animation: resultAppear 0.6s ease-out;
}

.result-section.hidden {
    display: none;
}

.result-label {
    font-size: 1.1rem;
    color: var(--text-light);
    margin-bottom: 15px;
    font-weight: 500;
}

.result-text {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color);
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    animation: resultPulse 0.6s ease-out;
}

@keyframes resultAppear {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes resultPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Share button styles */
.result-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.share-button,
.reset-button {
    border: none;
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 150px;
}

.share-button {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: var(--white);
}

.reset-button {
    background: linear-gradient(135deg, #FF9800, #F57C00);
    color: var(--white);
}

.share-button:hover,
.reset-button:hover {
    transform: translateY(-1px);
}

.share-button:hover {
    box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
}

.reset-button:hover {
    box-shadow: 0 4px 8px rgba(255, 152, 0, 0.3);
}

.share-button:active,
.reset-button:active {
    transform: translateY(0);
}

.share-icon,
.reset-icon {
    font-size: 1.1rem;
}

/* Navigation styles for landing pages */
.page-nav {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.nav-link {
    color: var(--primary-color);
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: var(--transition);
    border: 2px solid transparent;
}

.nav-link:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-1px);
}

/* Preset info styles */
.preset-info {
    background: linear-gradient(135deg, #f8f9fa, #e3f2fd);
    padding: 25px;
    border-radius: var(--border-radius);
    margin-bottom: 30px;
    text-align: center;
    border-left: 4px solid var(--primary-color);
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.preset-title {
    font-size: 1.4rem;
    color: var(--text-color);
    margin-bottom: 12px;
    font-weight: 700;
}

.preset-description {
    font-size: 1rem;
    color: var(--text-light);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* Usage examples styles */
.usage-examples {
    background: var(--secondary-color);
    padding: 25px;
    border-radius: var(--border-radius);
    animation: fadeInUp 0.8s ease-out 0.8s both;
}

.examples-title {
    font-size: 1.1rem;
    color: var(--text-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.examples-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.examples-list li {
    padding: 8px 0;
    color: var(--text-light);
    font-size: 0.95rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.examples-list li:last-child {
    border-bottom: none;
}

/* Food options specific styles */
.food-options {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
}

.food-option {
    background: var(--white);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Quick examples for custom page */
.quick-examples {
    margin-top: 15px;
    padding: 15px;
    background: rgba(76, 175, 80, 0.05);
    border-radius: 8px;
    border: 1px dashed var(--primary-color);
}

.quick-examples-title {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 10px;
    font-weight: 600;
}

.example-btn {
    background: var(--white);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    padding: 6px 12px;
    font-size: 0.85rem;
    border-radius: 15px;
    cursor: pointer;
    margin: 4px;
    transition: var(--transition);
    font-weight: 500;
}

.example-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-1px);
}

/* Install banner styles */
.install-banner {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    padding: 15px;
    border-radius: var(--border-radius);
    margin-top: 20px;
    animation: slideInUp 0.5s ease-out;
}

.install-banner.hidden {
    display: none;
}

.install-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    flex-wrap: wrap;
}

.install-icon {
    font-size: 1.2rem;
}

.install-text {
    flex: 1;
    font-size: 0.9rem;
    font-weight: 500;
}

.install-button {
    background: var(--white);
    color: var(--primary-color);
    border: none;
    padding: 8px 16px;
    font-size: 0.85rem;
    font-weight: 600;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
}

.install-button:hover {
    background: #f0f0f0;
    transform: translateY(-1px);
}

.dismiss-button {
    background: none;
    color: var(--white);
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 50%;
    transition: var(--transition);
}

.dismiss-button:hover {
    background: rgba(255, 255, 255, 0.1);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Options preview */
.options-preview {
    background: var(--secondary-color);
    padding: 20px;
    border-radius: var(--border-radius);
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

.preview-label {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 10px;
    font-weight: 600;
}

.options-list {
    font-size: 1rem;
    color: var(--text-color);
    font-weight: 500;
    line-height: 1.4;
}

/* Footer */
footer {
    text-align: center;
    margin-top: 40px;
    animation: fadeInUp 0.8s ease-out 0.8s both;
}

.footer-text {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-overlay.hidden {
    display: none;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--secondary-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design */
@media (max-width: 480px) {
    .container {
        padding: 15px;
    }
    
    .app-title {
        font-size: 2rem;
    }
    
    .spin-button {
        padding: 18px 35px;
        font-size: 1.2rem;
        min-width: 180px;
    }
    
    .result-text {
        font-size: 2rem;
    }
    
    .input-section,
    .result-section,
    .options-preview {
        padding: 20px;
    }
}

@media (max-width: 320px) {
    .app-title {
        font-size: 1.8rem;
    }
    
    .spin-button {
        padding: 15px 30px;
        font-size: 1.1rem;
        min-width: 160px;
    }
    
    .result-text {
        font-size: 1.8rem;
        letter-spacing: 1px;
    }
}

/* High DPI screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .app-title {
        text-rendering: optimizeLegibility;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --background: #1a1a1a;
        --white: #2d2d2d;
        --secondary-color: #3a3a3a;
        --text-color: #e0e0e0;
        --text-light: #b0b0b0;
        --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        --shadow-hover: 0 6px 12px rgba(0, 0, 0, 0.4);
    }
    
    .options-input {
        background: #3a3a3a;
        border-color: #555;
        color: var(--text-color);
    }
    
    .options-input:focus {
        background: #2d2d2d;
    }
}

/* Print styles */
@media print {
    .spin-button,
    .loading-overlay {
        display: none !important;
    }
    
    .result-section {
        border: 2px solid #000 !important;
    }
}

/* Category Grid Styles */
.categories-section {
    margin-bottom: 30px;
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.section-title {
    font-size: 1.5rem;
    color: var(--text-color);
    text-align: center;
    margin-bottom: 25px;
    font-weight: 600;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.category-btn {
    background: var(--white);
    border: 2px solid #e0e0e0;
    border-radius: var(--border-radius);
    padding: 20px 15px;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.category-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
}

.category-btn:active {
    transform: translateY(-1px);
}

.category-btn.selected {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.category-btn.selected .category-title,
.category-btn.selected .category-desc {
    color: white;
}

.category-icon {
    font-size: 2rem;
    margin-bottom: 5px;
}

.category-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 3px;
}

.category-desc {
    font-size: 0.75rem;
    color: var(--text-light);
    line-height: 1.3;
}

.custom-toggle {
    text-align: center;
    margin-top: 20px;
}

.toggle-btn {
    background: transparent;
    border: 2px dashed #ccc;
    border-radius: var(--border-radius);
    padding: 15px 25px;
    cursor: pointer;
    color: var(--text-light);
    font-size: 0.9rem;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.toggle-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(76, 175, 80, 0.05);
}

.toggle-icon {
    font-size: 1.1rem;
}

/* Responsive adjustments for category grid */
@media (max-width: 480px) {
    .category-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
    
    .category-btn {
        padding: 18px 12px;
    }
    
    .category-icon {
        font-size: 1.8rem;
    }
    
    .category-title {
        font-size: 0.85rem;
    }
    
    .category-desc {
        font-size: 0.7rem;
    }
}
