/**
 * SpinChoice - Analytics Configuration
 * Replace the GA4 measurement ID with your actual ID
 */

// Analytics Configuration
const ANALYTICS_CONFIG = {
    // Replace with your Google Analytics 4 Measurement ID
    GA4_MEASUREMENT_ID: 'G-XXXXXXXXXX', // Get this from Google Analytics
    
    // Custom event names for better organization
    EVENTS: {
        PAGE_LOADED: 'page_loaded',
        CATEGORY_SELECTED: 'category_selected',
        SPIN_WHEEL: 'spin_wheel',
        RESULT_SHOWN: 'result_shown',
        SHARE_ATTEMPTED: 'share_attempted',
        SHARE_COMPLETED: 'share_completed',
        RESET_DECISION: 'reset_decision',
        PWA_INSTALLED: 'pwa_installed',
        PWA_LAUNCHED: 'pwa_launched',
        SESSION_ENDED: 'session_ended',
        ERROR_OCCURRED: 'error_occurred'
    },
    
    // Categories for better organization in GA4
    CATEGORIES: {
        USER_ENGAGEMENT: 'User Engagement',
        PWA_FEATURES: 'PWA Features',
        ERRORS: 'Errors',
        PERFORMANCE: 'Performance'
    },
    
    // Enable/disable analytics (useful for development)
    ENABLED: true,
    
    // Debug mode (shows console logs)
    DEBUG: window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ANALYTICS_CONFIG;
}

// Make available globally
window.ANALYTICS_CONFIG = ANALYTICS_CONFIG;
