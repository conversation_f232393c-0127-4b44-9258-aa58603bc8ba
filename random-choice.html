<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Decision Wheel | Create Your Own SpinChoice</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Create your own custom decision wheel with SpinChoice! Add any options you want and let the wheel decide. Perfect for personal choices, games, and creative decisions.">
    <meta name="keywords" content="custom decision wheel, create choice wheel, personalized picker, custom options, decision maker, random picker generator">
    <meta name="author" content="SpinChoice">
    <meta property="og:title" content="Custom Decision Wheel | Create Your Own SpinChoice">
    <meta property="og:description" content="Build your own decision wheel! Add any options and let SpinChoice pick for you. Unlimited customization for any choice scenario.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="">
    <meta property="og:image" content="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><circle cx='256' cy='256' r='240' fill='%234CAF50'/><text x='256' y='320' text-anchor='middle' fill='white' font-size='120' font-family='Arial'>⚙️</text></svg>">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Custom Decision Wheel | SpinChoice">
    <meta name="twitter:description" content="Create your own decision wheel with any options! Perfect for personal choices and creative decisions.">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4CAF50">
    <meta name="description" content="SpinChoice - A simple random decision maker PWA">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Apple Touch Icon -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SpinChoice">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='45' fill='%234CAF50'/><text x='50' y='58' text-anchor='middle' fill='white' font-size='24' font-family='Arial'>🎯</text></svg>">
    
    <!-- Styles -->
    <link rel="stylesheet" href="style.css">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "SpinChoice Custom Wheel",
      "description": "Create custom decision wheels with your own options using SpinChoice",
      "applicationCategory": "Utility",
      "operatingSystem": "Any",
      "offers": {
        "@type": "Offer",
        "price": "0"
      },
      "featureList": [
        "Custom option creation",
        "Personalized decision making",
        "Unlimited choices",
        "Mobile-friendly design",
        "Offline functionality"
      ]
    }
    </script>
</head>
<body>
    <div class="container">
        <header>
            <h1 class="app-title">⚙️ Custom Wheel</h1>
            <p class="subtitle">Create your own decision wheel</p>
            <nav class="page-nav">
                <a href="index.html" class="nav-link">All Options</a>
                <a href="yes-no.html" class="nav-link">Yes/No</a>
                <a href="what-to-eat.html" class="nav-link">Food Choices</a>
            </nav>
        </header>

        <main class="main-content">
            <!-- Preset Info -->
            <div class="preset-info">
                <h2 class="preset-title">🎨 Build Your Decision Wheel</h2>
                <p class="preset-description">
                    Create a personalized decision wheel with your own options! 
                    Add anything you want - colors, movies, activities, or life choices. 
                    The possibilities are endless!
                </p>
            </div>

            <!-- Custom Options Input (Visible for custom page) -->
            <div class="input-section">
                <label for="custom-options" class="input-label">
                    ✏️ Enter your custom options (one per line or comma-separated):
                </label>
                <textarea 
                    id="custom-options" 
                    class="options-input" 
                    placeholder="Option 1, Option 2, Option 3&#10;Or type each option on a new line..."
                    rows="6"
                ></textarea>
                <p class="input-help">💡 Pro tip: Add as many options as you want - the more the merrier!</p>
                
                <!-- Quick Examples -->
                <div class="quick-examples">
                    <p class="quick-examples-title">Quick examples to try:</p>
                    <button class="example-btn" data-options="Red, Blue, Green, Yellow, Purple">Colors</button>
                    <button class="example-btn" data-options="Netflix, YouTube, Read a book, Go for a walk">Activities</button>
                    <button class="example-btn" data-options="Monday, Tuesday, Wednesday, Thursday, Friday">Weekdays</button>
                    <button class="example-btn" data-options="Action, Comedy, Drama, Horror, Romance">Movie Genres</button>
                </div>
            </div>

            <!-- Spin Button -->
            <div class="spin-section">
                <button id="spin-btn" class="spin-button" disabled>
                    <span class="spin-text">🎲 ADD OPTIONS TO SPIN</span>
                </button>
            </div>

            <!-- Result Display -->
            <div id="result-section" class="result-section hidden">
                <div class="result-label">Your choice is:</div>
                <div id="result-text" class="result-text"></div>
                <button id="share-btn" class="share-button">
                    <span class="share-icon">📤</span>
                    Share Result
                </button>
            </div>

            <!-- Options Preview -->
            <div class="options-preview">
                <p class="preview-label">Your wheel options:</p>
                <div id="options-list" class="options-list">Add your options above to get started!</div>
            </div>

            <!-- Tips for Custom Wheels -->
            <div class="usage-examples">
                <h3 class="examples-title">Great custom wheel ideas:</h3>
                <ul class="examples-list">
                    <li>🎬 Movie night selections</li>
                    <li>🎨 Art project ideas</li>
                    <li>🏃 Exercise routines</li>
                    <li>📚 Book genres to read</li>
                    <li>🌍 Travel destinations</li>
                    <li>🎵 Music genres to listen to</li>
                    <li>🎯 Weekend activity planner</li>
                    <li>🍳 Recipe ingredients</li>
                </ul>
            </div>
        </main>

        <footer>
            <p class="footer-text">🎯 SpinChoice v1.0 - Make decisions easier</p>
            <!-- Install App Banner -->
            <div id="install-banner" class="install-banner hidden">
                <div class="install-content">
                    <span class="install-icon">📱</span>
                    <span class="install-text">Install SpinChoice for quick access!</span>
                    <button id="install-btn" class="install-button">Install</button>
                    <button id="dismiss-install" class="dismiss-button">×</button>
                </div>
            </div>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="spinner"></div>
    </div>

    <!-- Scripts -->
    <script src="app.js"></script>
    <script>
        // Custom page specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Handle example buttons
            const exampleBtns = document.querySelectorAll('.example-btn');
            exampleBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const options = this.getAttribute('data-options');
                    document.getElementById('custom-options').value = options;
                    if (window.spinChoiceApp) {
                        window.spinChoiceApp.updateOptions();
                    }
                });
            });
        });
    </script>
</body>
</html>
