<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SpinChoice - Random Decision Maker | Make Choices Easy</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="SpinChoice - The ultimate random decision maker PWA. Spin the wheel for Yes/No decisions or create custom choice wheels. Free, fast, and works offline!">
    <meta name="keywords" content="decision maker, random choice, spin wheel, yes no, decision wheel, choice maker, random picker">
    <meta name="author" content="SpinChoice">
    <meta property="og:title" content="SpinChoice - Random Decision Maker">
    <meta property="og:description" content="Make decisions easier with SpinChoice! Spin for Yes/No or create custom choice wheels. Works offline!">
    <meta property="og:type" content="website">
    <meta property="og:url" content="">
    <meta property="og:image" content="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><circle cx='256' cy='256' r='240' fill='%234CAF50'/><text x='256' y='320' text-anchor='middle' fill='white' font-size='200' font-family='Arial'>🎯</text></svg>">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="SpinChoice - Random Decision Maker">
    <meta name="twitter:description" content="Make decisions easier with SpinChoice! Spin for Yes/No or create custom choice wheels.">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4CAF50">
    <meta name="description" content="SpinChoice - A simple random decision maker PWA">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Apple Touch Icon -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SpinChoice">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='45' fill='%234CAF50'/><text x='50' y='58' text-anchor='middle' fill='white' font-size='24' font-family='Arial'>🎯</text></svg>">
    
    <!-- Google Analytics 4 -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-XXXXXXXXXX', {
            page_title: 'SpinChoice - Decision Maker',
            page_location: window.location.href,
            send_page_view: false // We'll send custom events
        });
        
        // Custom event tracking function
        window.trackEvent = function(eventName, parameters = {}) {
            if (typeof gtag !== 'undefined') {
                gtag('event', eventName, {
                    event_category: 'SpinChoice',
                    event_label: parameters.label || '',
                    value: parameters.value || 0,
                    ...parameters
                });
            }
            console.log('Analytics Event:', eventName, parameters);
        };
    </script>
    
    <!-- Styles -->
    <link rel="stylesheet" href="style.css?v=1.3.0">
</head>
<body>
    <div class="container">
        <header>
            <h1 class="app-title">🎯 SpinChoice</h1>
            <p class="subtitle">Let fate decide for you</p>
        </header>

        <main class="main-content">
            <!-- Decision Categories -->
            <div class="categories-section">
                <h2 class="section-title">🎯 Choose Your Decision Type</h2>
                <div class="category-grid">
                    <button class="category-btn" data-options="Yes, No">
                        <span class="category-icon">✅</span>
                        <span class="category-title">Yes or No</span>
                        <span class="category-desc">Simple binary decisions</span>
                    </button>
                    
                    <button class="category-btn" data-options="Pizza, Burgers, Salad, Tacos, Chinese Food, Italian, Mexican">
                        <span class="category-icon">🍕</span>
                        <span class="category-title">What to Eat</span>
                        <span class="category-desc">Food & restaurant choices</span>
                    </button>
                    
                    <button class="category-btn" data-options="Netflix, YouTube, Read a Book, Go for a Walk, Play Games, Listen to Music">
                        <span class="category-icon">🎬</span>
                        <span class="category-title">Entertainment</span>
                        <span class="category-desc">Movies, shows & activities</span>
                    </button>
                    
                    <button class="category-btn" data-options="Work Out, Take a Break, Study, Clean House, Call a Friend, Go Shopping">
                        <span class="category-icon">📝</span>
                        <span class="category-title">Daily Tasks</span>
                        <span class="category-desc">Productive activities</span>
                    </button>
                    
                    <button class="category-btn" data-options="Red, Blue, Green, Yellow, Purple, Orange, Black, White">
                        <span class="category-icon">🎨</span>
                        <span class="category-title">Colors</span>
                        <span class="category-desc">Pick a color</span>
                    </button>
                    
                    <button class="category-btn" data-options="Beach, Mountains, City, Forest, Desert, Lake">
                        <span class="category-icon">🏖️</span>
                        <span class="category-title">Travel</span>
                        <span class="category-desc">Vacation destinations</span>
                    </button>
                </div>
                
                <!-- Custom Option Toggle -->
                <div class="custom-toggle">
                    <button id="custom-toggle-btn" class="toggle-btn">
                        <span class="toggle-icon">⚙️</span>
                        Or create your own options
                    </button>
                </div>
            </div>

            <!-- Custom Options Input (Initially Hidden) -->
            <div class="input-section" id="custom-input-section" style="display: none;">
                <label for="custom-options" class="input-label">
                    ✏️ Enter your custom options (one per line or comma-separated):
                </label>
                <textarea 
                    id="custom-options" 
                    class="options-input" 
                    placeholder="Option 1, Option 2, Option 3..."
                    rows="4"
                ></textarea>
                <p class="input-help">💡 Add as many options as you want!</p>
            </div>

            <!-- Spin Button -->
            <div class="spin-section">
                <button id="spin-btn" class="spin-button">
                    <span class="spin-text">🎲 SPIN</span>
                </button>
            </div>

            <!-- Result Display -->
            <div id="result-section" class="result-section hidden">
                <div class="result-label">Your choice is:</div>
                <div id="result-text" class="result-text"></div>
                <div class="result-actions">
                    <button id="share-btn" class="share-button">
                        <span class="share-icon">📤</span>
                        Share Result
                    </button>
                    <button id="reset-btn" class="reset-button">
                        <span class="reset-icon">🔄</span>
                        Make Another Choice
                    </button>
                </div>
            </div>

            <!-- Options Preview -->
            <div class="options-preview">
                <p class="preview-label">Current options:</p>
                <div id="options-list" class="options-list">Yes, No</div>
            </div>
        </main>

        <footer>
            <p class="footer-text">🎯 SpinChoice v1.0 - Make decisions easier</p>
            <!-- Install App Banner -->
            <div id="install-banner" class="install-banner hidden">
                <div class="install-content">
                    <span class="install-icon">📱</span>
                    <span class="install-text">Install SpinChoice for quick access!</span>
                    <button id="install-btn" class="install-button">Install</button>
                    <button id="dismiss-install" class="dismiss-button">×</button>
                </div>
            </div>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="spinner"></div>
    </div>

    <!-- Scripts -->
    <script src="analytics-config.js?v=1.3.0"></script>
    <script src="app-simple.js?v=1.3.0"></script>
</body>
</html>
