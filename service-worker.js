/**
 * SpinChoice PWA - Service Worker
 * Handles caching for offline functionality
 */

const CACHE_NAME = 'spinchoice-v1.2.0';
const urlsToCache = [
    './',
    './index.html',
    './yes-no.html',
    './what-to-eat.html',
    './random-choice.html',
    './style.css',
    './app-simple.js',
    './manifest.json'
];

// Install event - cache resources
self.addEventListener('install', (event) => {
    console.log('Service Worker: Install event');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => {
                console.log('Service Worker: Caching files');
                return cache.addAll(urlsToCache);
            })
            .then(() => {
                console.log('Service Worker: All files cached');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Service Worker: Cache failed', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker: Activate event');
    
    event.waitUntil(
        caches.keys().then((cacheNames) => {
            return Promise.all(
                cacheNames.map((cacheName) => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('Service Worker: Deleting old cache', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker: Claiming clients');
            return self.clients.claim();
        })
    );
});

// Fetch event - serve cached content when offline
// Fetch event - Network First strategy for better development experience
self.addEventListener('fetch', (event) => {
    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }
    
    // Skip chrome-extension and other non-http requests
    if (!event.request.url.startsWith('http')) {
        return;
    }
    
    event.respondWith(
        // Try network first
        fetch(event.request)
            .then((response) => {
                // If successful, update cache and return response
                if (response && response.status === 200 && response.type === 'basic') {
                    const responseToCache = response.clone();
                    
                    caches.open(CACHE_NAME)
                        .then((cache) => {
                            if (event.request.url.startsWith(self.location.origin)) {
                                cache.put(event.request, responseToCache);
                                console.log('Service Worker: Updated cache for', event.request.url);
                            }
                        });
                }
                
                return response;
            })
            .catch(() => {
                // If network fails, try cache as fallback
                console.log('Service Worker: Network failed, trying cache for', event.request.url);
                return caches.match(event.request)
                    .then((cachedResponse) => {
                        if (cachedResponse) {
                            console.log('Service Worker: Serving from cache (offline)', event.request.url);
                            return cachedResponse;
                        }
                        
                        // If it's a navigation request and we have a cached index.html, serve it
                        if (event.request.mode === 'navigate') {
                            return caches.match('./index.html');
                        }
                        
                        // Return a basic offline response
                        return new Response('Offline - Content not available', {
                            status: 503,
                            statusText: 'Service Unavailable',
                            headers: new Headers({
                                'Content-Type': 'text/plain'
                            })
                        });
                    });
            })
    );
});

// Background sync for future features (optional)
self.addEventListener('sync', (event) => {
    console.log('Service Worker: Background sync', event.tag);
    
    if (event.tag === 'background-sync') {
        event.waitUntil(
            // Handle background sync tasks here
            Promise.resolve()
        );
    }
});

// Push notification handling (for future features)
self.addEventListener('push', (event) => {
    console.log('Service Worker: Push message received');
    
    // Handle push notifications here if needed
    const options = {
        body: 'SpinChoice has new features!',
        icon: './icon-192x192.png',
        badge: './icon-72x72.png',
        tag: 'spinchoice-update'
    };
    
    if (event.data) {
        const data = event.data.json();
        options.body = data.body || options.body;
    }
    
    event.waitUntil(
        self.registration.showNotification('SpinChoice', options)
    );
});

// Error tracking for service worker
self.addEventListener('error', (event) => {
    console.error('Service Worker Error:', event.error);
    
    // You could send this to your analytics endpoint
    // fetch('/api/error-tracking', { ... })
});

// Performance monitoring
self.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'PERFORMANCE_REPORT') {
        console.log('Performance metrics:', event.data.metrics);
        // Could send to analytics service
    }
});

// Push notification handling (for future features)
self.addEventListener('push', (event) => {
    console.log('Service Worker: Push received', event);
    
    const options = {
        body: event.data ? event.data.text() : 'SpinChoice notification',
        icon: 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 96 96"><circle cx="48" cy="48" r="44" fill="%234CAF50"/><text x="48" y="60" text-anchor="middle" fill="white" font-size="36" font-family="Arial">🎯</text></svg>',
        badge: 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 96 96"><circle cx="48" cy="48" r="44" fill="%234CAF50"/><text x="48" y="60" text-anchor="middle" fill="white" font-size="36" font-family="Arial">🎯</text></svg>',
        vibrate: [200, 100, 200],
        tag: 'spinchoice-notification',
        renotify: true,
        requireInteraction: false,
        silent: false
    };
    
    event.waitUntil(
        self.registration.showNotification('SpinChoice', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
    console.log('Service Worker: Notification click', event);
    
    event.notification.close();
    
    event.waitUntil(
        clients.matchAll({ type: 'window' }).then((clientList) => {
            // Focus existing window if available
            for (const client of clientList) {
                if (client.url === self.location.origin && 'focus' in client) {
                    return client.focus();
                }
            }
            // Open new window if no existing window found
            if (clients.openWindow) {
                return clients.openWindow('./');
            }
        })
    );
});

// Message handling from main app
self.addEventListener('message', (event) => {
    console.log('Service Worker: Message received', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    // Send response back to main app
    event.ports[0]?.postMessage({
        type: 'SW_RESPONSE',
        message: 'Service Worker is active'
    });
});

// Error handling
self.addEventListener('error', (event) => {
    console.error('Service Worker: Error', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
    console.error('Service Worker: Unhandled Promise Rejection', event.reason);
    event.preventDefault();
});

// Periodic background sync (for future features)
self.addEventListener('periodicsync', (event) => {
    console.log('Service Worker: Periodic sync', event.tag);
    
    if (event.tag === 'content-sync') {
        event.waitUntil(
            // Handle periodic sync tasks here
            Promise.resolve()
        );
    }
});

console.log('Service Worker: Script loaded successfully');
