{"name": "SpinChoice - Decision Maker", "short_name": "SpinChoice", "description": "The ultimate random decision maker PWA. Make choices easy with custom wheels, Yes/No decisions, and food picking. Works offline!", "version": "1.1.0", "start_url": "./index.html", "display": "standalone", "background_color": "#ffffff", "theme_color": "#4CAF50", "orientation": "portrait-primary", "scope": "./", "lang": "en", "dir": "ltr", "categories": ["utilities", "productivity"], "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 192 192'><circle cx='96' cy='96' r='88' fill='%234CAF50'/><text x='96' y='120' text-anchor='middle' fill='white' font-size='72' font-family='Arial'>🎯</text></svg>", "sizes": "192x192", "type": "image/svg+xml", "purpose": "any maskable"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><circle cx='256' cy='256' r='240' fill='%234CAF50'/><text x='256' y='320' text-anchor='middle' fill='white' font-size='200' font-family='Arial'>🎯</text></svg>", "sizes": "512x512", "type": "image/svg+xml", "purpose": "any maskable"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 144 144'><circle cx='72' cy='72' r='64' fill='%234CAF50'/><text x='72' y='90' text-anchor='middle' fill='white' font-size='54' font-family='Arial'>🎯</text></svg>", "sizes": "144x144", "type": "image/svg+xml", "purpose": "any"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 96 96'><circle cx='48' cy='48' r='44' fill='%234CAF50'/><text x='48' y='60' text-anchor='middle' fill='white' font-size='36' font-family='Arial'>🎯</text></svg>", "sizes": "96x96", "type": "image/svg+xml", "purpose": "any"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 72 72'><circle cx='36' cy='36' r='32' fill='%234CAF50'/><text x='36' y='45' text-anchor='middle' fill='white' font-size='27' font-family='Arial'>🎯</text></svg>", "sizes": "72x72", "type": "image/svg+xml", "purpose": "any"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 48 48'><circle cx='24' cy='24' r='22' fill='%234CAF50'/><text x='24' y='30' text-anchor='middle' fill='white' font-size='18' font-family='Arial'>🎯</text></svg>", "sizes": "48x48", "type": "image/svg+xml", "purpose": "any"}], "shortcuts": [{"name": "Yes or No", "short_name": "Yes/No", "description": "Quick Yes or No decision wheel", "url": "./yes-no.html", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 96 96'><circle cx='48' cy='48' r='44' fill='%234CAF50'/><text x='48' y='60' text-anchor='middle' fill='white' font-size='24' font-family='Arial'>✅</text></svg>", "sizes": "96x96", "type": "image/svg+xml"}]}, {"name": "Food Picker", "short_name": "Food", "description": "What to eat decision wheel", "url": "./what-to-eat.html", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 96 96'><circle cx='48' cy='48' r='44' fill='%234CAF50'/><text x='48' y='60' text-anchor='middle' fill='white' font-size='24' font-family='Arial'>🍕</text></svg>", "sizes": "96x96", "type": "image/svg+xml"}]}, {"name": "Custom Wheel", "short_name": "Custom", "description": "Create your own decision wheel", "url": "./random-choice.html", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 96 96'><circle cx='48' cy='48' r='44' fill='%234CAF50'/><text x='48' y='60' text-anchor='middle' fill='white' font-size='24' font-family='Arial'>⚙️</text></svg>", "sizes": "96x96", "type": "image/svg+xml"}]}], "screenshots": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 375 667'><rect width='375' height='667' fill='%23fafafa'/><rect x='20' y='60' width='335' height='100' rx='12' fill='%234CAF50'/><text x='187' y='115' text-anchor='middle' fill='white' font-size='24' font-weight='bold'>🎯 SpinChoice</text><rect x='20' y='200' width='335' height='150' rx='12' fill='white' stroke='%23e0e0e0'/><text x='40' y='230' fill='%23666' font-size='14'>Enter your options:</text><rect x='40' y='240' width='295' height='80' rx='8' fill='%23f5f5f5' stroke='%23ddd'/><text x='50' y='265' fill='%23333' font-size='16'>Yes, No</text><circle cx='187' cy='450' r='60' fill='%234CAF50'/><text x='187' y='460' text-anchor='middle' fill='white' font-size='18' font-weight='bold'>🎲 SPIN</text></svg>", "sizes": "375x667", "type": "image/svg+xml", "form_factor": "narrow"}], "prefer_related_applications": false, "related_applications": [], "file_handlers": [{"action": "./", "accept": {"text/plain": [".txt"]}}], "share_target": {"action": "./", "method": "GET", "params": {"title": "title", "text": "text", "url": "url"}}, "launch_handler": {"client_mode": "focus-existing"}, "edge_side_panel": {"preferred_width": 400}, "protocol_handlers": [{"protocol": "web+spinchoice", "url": "./?action=%s"}]}