/**
 * SpinChoice PWA - Simplified with Category Selection
 */

class SpinChoice {
    constructor() {
        this.isSpinning = false;
        this.defaultOptions = ['Yes', 'No'];
        this.currentOptions = [...this.defaultOptions];
        this.deferredPrompt = null;
        this.sessionData = {
            spins: 0,
            startTime: Date.now(),
            categories: new Set()
        };
        
        this.initializeElements();
        this.bindEvents();
        this.updateOptionsPreview();
        this.initializePWA();
        this.initializeAnalytics();
    }

    initializeElements() {
        this.optionsInput = document.getElementById('custom-options');
        this.spinBtn = document.getElementById('spin-btn');
        this.resultSection = document.getElementById('result-section');
        this.resultText = document.getElementById('result-text');
        this.optionsList = document.getElementById('options-list');
        this.shareBtn = document.getElementById('share-btn');
        this.resetBtn = document.getElementById('reset-btn');
    }

    bindEvents() {
        // Spin button
        this.spinBtn.addEventListener('click', () => this.handleSpin());
        
        // Category buttons
        const categoryButtons = document.querySelectorAll('.category-btn');
        categoryButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleCategorySelect(e));
        });
        
        // Custom toggle button
        const customToggleBtn = document.getElementById('custom-toggle-btn');
        const customInputSection = document.getElementById('custom-input-section');
        if (customToggleBtn && customInputSection) {
            customToggleBtn.addEventListener('click', () => {
                const isHidden = customInputSection.style.display === 'none';
                customInputSection.style.display = isHidden ? 'block' : 'none';
                customToggleBtn.innerHTML = isHidden 
                    ? '<span class="toggle-icon">📝</span>Hide custom options'
                    : '<span class="toggle-icon">⚙️</span>Or create your own options';
                
                if (isHidden) {
                    this.clearCategorySelections();
                    this.optionsInput.focus();
                }
            });
        }
        
        // Options input
        if (this.optionsInput) {
            this.optionsInput.addEventListener('input', () => {
                this.updateOptionsFromInput();
            });
        }

        // Share button
        if (this.shareBtn) {
            this.shareBtn.addEventListener('click', () => this.handleShare());
        }

        // Reset button
        if (this.resetBtn) {
            this.resetBtn.addEventListener('click', () => this.handleReset());
        }
    }

    handleCategorySelect(event) {
        const categoryBtn = event.currentTarget;
        const options = categoryBtn.dataset.options;
        const categoryTitle = categoryBtn.querySelector('.category-title').textContent;
        
        // Track category selection
        this.trackEvent('category_selected', {
            category: categoryTitle,
            options_count: options.split(',').length
        });
        this.sessionData.categories.add(categoryTitle);
        
        // Clear previous selections
        this.clearCategorySelections();
        
        // Mark this category as selected
        categoryBtn.classList.add('selected');
        
        // Update options
        this.setOptionsFromString(options);
        
        // Hide custom input section
        const customInputSection = document.getElementById('custom-input-section');
        if (customInputSection) {
            customInputSection.style.display = 'none';
        }
        
        // Update toggle button text
        const customToggleBtn = document.getElementById('custom-toggle-btn');
        if (customToggleBtn) {
            customToggleBtn.innerHTML = '<span class="toggle-icon">⚙️</span>Or create your own options';
        }
    }

    clearCategorySelections() {
        const categoryButtons = document.querySelectorAll('.category-btn');
        categoryButtons.forEach(btn => btn.classList.remove('selected'));
    }

    setOptionsFromString(optionsString) {
        if (!optionsString || optionsString.trim() === '') {
            this.currentOptions = [...this.defaultOptions];
        } else {
            const options = optionsString
                .split(/[,\n]/)
                .map(option => option.trim())
                .filter(option => option.length > 0);
            
            this.currentOptions = options.length > 0 ? options : [...this.defaultOptions];
        }
        
        if (this.optionsInput) {
            this.optionsInput.value = this.currentOptions.join(', ');
        }
        
        this.updateOptionsPreview();
    }

    updateOptionsFromInput() {
        const inputValue = this.optionsInput ? this.optionsInput.value.trim() : '';
        this.setOptionsFromString(inputValue);
        this.clearCategorySelections();
    }

    updateOptionsPreview() {
        if (this.optionsList) {
            this.optionsList.textContent = this.currentOptions.join(', ');
        }
    }

    async handleSpin() {
        if (this.isSpinning) return;
        
        this.isSpinning = true;
        this.spinBtn.disabled = true;
        this.sessionData.spins++;
        
        // Track spin event
        this.trackEvent('spin_wheel', {
            options_count: this.currentOptions.length,
            spin_number: this.sessionData.spins,
            options_type: this.getOptionsType()
        });
        
        // Hide previous result
        if (this.resultSection) {
            this.resultSection.classList.add('hidden');
        }
        
        // Animate
        await this.animateSpin();
        
        // Get random result
        const result = this.getRandomOption();
        
        // Track result
        this.trackEvent('result_shown', {
            result: result,
            options_count: this.currentOptions.length,
            spin_number: this.sessionData.spins
        });
        
        // Show result
        this.showResult(result);
        
        this.isSpinning = false;
        this.spinBtn.disabled = false;
    }

    async animateSpin() {
        const spinText = this.spinBtn.querySelector('.spin-text');
        const originalText = spinText.textContent;
        
        // Animate spin button
        this.spinBtn.classList.add('spinning');
        
        // Cycle through options
        for (let i = 0; i < 20; i++) {
            const randomOption = this.currentOptions[Math.floor(Math.random() * this.currentOptions.length)];
            spinText.textContent = `🎲 ${randomOption}`;
            await new Promise(resolve => setTimeout(resolve, 100 + i * 10));
        }
        
        spinText.textContent = originalText;
        this.spinBtn.classList.remove('spinning');
    }

    getRandomOption() {
        return this.currentOptions[Math.floor(Math.random() * this.currentOptions.length)];
    }

    showResult(result) {
        if (this.resultText) {
            this.resultText.textContent = result;
        }
        
        if (this.resultSection) {
            this.resultSection.classList.remove('hidden');
            
            // Add celebration animation
            this.resultText.style.animation = 'bounce 0.6s ease-out';
            setTimeout(() => {
                if (this.resultText) {
                    this.resultText.style.animation = '';
                }
            }, 600);
        }
    }

    async handleShare() {
        const result = this.resultText ? this.resultText.textContent : '';
        const text = `SpinChoice decided: ${result}! 🎯`;
        
        // Track share attempt
        this.trackEvent('share_attempted', {
            result: result,
            method: navigator.share ? 'native_share' : 'clipboard'
        });
        
        if (navigator.share) {
            try {
                await navigator.share({
                    title: 'SpinChoice Result',
                    text: text,
                    url: window.location.href
                });
                this.trackEvent('share_completed', { method: 'native_share' });
            } catch (err) {
                console.log('Share cancelled or failed');
                this.trackEvent('share_cancelled', { method: 'native_share' });
            }
        } else {
            // Fallback: copy to clipboard
            try {
                await navigator.clipboard.writeText(text);
                this.showShareSuccess();
                this.trackEvent('share_completed', { method: 'clipboard' });
            } catch (err) {
                console.log('Copy failed');
                this.trackEvent('share_failed', { method: 'clipboard', error: err.message });
            }
        }
    }

    showShareSuccess() {
        const shareBtn = this.shareBtn;
        const originalText = shareBtn.innerHTML;
        shareBtn.innerHTML = '<span class="share-icon">✅</span>Copied!';
        shareBtn.style.background = '#4CAF50';
        
        setTimeout(() => {
            shareBtn.innerHTML = originalText;
            shareBtn.style.background = '';
        }, 2000);
    }

    handleReset() {
        // Track reset action
        this.trackEvent('reset_decision', {
            session_spins: this.sessionData.spins
        });
        
        // Hide the result section
        if (this.resultSection) {
            this.resultSection.classList.add('hidden');
        }
        
        // Clear the result text
        if (this.resultText) {
            this.resultText.textContent = '';
        }
        
        // Re-enable spin button if disabled
        if (this.spinBtn) {
            this.spinBtn.disabled = false;
        }
        
        // Add a nice animation
        if (this.resetBtn) {
            this.resetBtn.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.resetBtn.style.transform = '';
            }, 150);
        }
    }

    initializePWA() {
        // Check for service worker updates
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then((registration) => {
                // Check for updates every 60 seconds
                setInterval(() => {
                    registration.update();
                }, 60000);
            });

            // Listen for service worker updates
            navigator.serviceWorker.addEventListener('controllerchange', () => {
                console.log('New service worker activated, reloading...');
                window.location.reload();
            });
        }

        // PWA Install prompt handling
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA install prompt available');
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallBanner();
        });

        // Install button click handler
        const installBtn = document.getElementById('install-btn');
        if (installBtn) {
            installBtn.addEventListener('click', () => {
                this.handleInstall();
            });
        }

        // Dismiss install banner
        const dismissBtn = document.getElementById('dismiss-install');
        if (dismissBtn) {
            dismissBtn.addEventListener('click', () => {
                this.hideInstallBanner();
            });
        }

        // Check if already installed
        window.addEventListener('appinstalled', () => {
            console.log('PWA was installed');
            this.hideInstallBanner();
        });
    }

    showInstallBanner() {
        const installBanner = document.getElementById('install-banner');
        if (installBanner) {
            installBanner.classList.remove('hidden');
        }
    }

    hideInstallBanner() {
        const installBanner = document.getElementById('install-banner');
        if (installBanner) {
            installBanner.classList.add('hidden');
        }
        this.deferredPrompt = null;
    }

    async handleInstall() {
        if (!this.deferredPrompt) return;

        try {
            await this.deferredPrompt.prompt();
            const { outcome } = await this.deferredPrompt.userChoice;
            
            if (outcome === 'accepted') {
                console.log('User accepted the install prompt');
            } else {
                console.log('User dismissed the install prompt');
            }
        } catch (error) {
            console.error('Install failed:', error);
        }

        this.hideInstallBanner();
    }

    // Analytics and Monitoring Methods
    initializeAnalytics() {
        // Track page load
        this.trackEvent('page_loaded', {
            user_agent: navigator.userAgent,
            screen_resolution: `${screen.width}x${screen.height}`,
            is_mobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
        });

        // Track PWA usage
        if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
            this.trackEvent('pwa_launched', { mode: 'standalone' });
        }

        // Track session duration on page unload
        window.addEventListener('beforeunload', () => {
            const sessionDuration = Date.now() - this.sessionData.startTime;
            this.trackEvent('session_ended', {
                duration_seconds: Math.round(sessionDuration / 1000),
                total_spins: this.sessionData.spins,
                categories_used: Array.from(this.sessionData.categories).join(',')
            });
        });

        // Track errors
        window.addEventListener('error', (event) => {
            this.trackEvent('javascript_error', {
                error_message: event.message,
                error_source: event.filename,
                error_line: event.lineno
            });
        });

        // Track unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.trackEvent('promise_rejection', {
                error_reason: event.reason
            });
        });
    }

    trackEvent(eventName, parameters = {}) {
        // Use global tracking function if available
        if (typeof window.trackEvent === 'function') {
            window.trackEvent(eventName, parameters);
        }
        
        // Also log to console for debugging
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log('🔍 Analytics Event:', eventName, parameters);
        }
    }

    getOptionsType() {
        // Determine if using predefined category or custom options
        const selectedCategory = document.querySelector('.category-btn.selected');
        if (selectedCategory) {
            return selectedCategory.querySelector('.category-title').textContent;
        }
        return 'custom';
    }

    // Error Reporting
    reportError(error, context = '') {
        this.trackEvent('app_error', {
            error_message: error.message || error,
            error_context: context,
            timestamp: new Date().toISOString()
        });
        
        console.error('App Error:', error, 'Context:', context);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SpinChoice();
});
