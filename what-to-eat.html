<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>What to Eat Decision Maker | SpinChoice Food Wheel</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Can't decide what to eat? Use SpinChoice's food decision wheel! Choose between Pizza, Burgers, Salad, and Tacos. Perfect for meal planning and eliminating food indecision.">
    <meta name="keywords" content="what to eat, food decision, meal picker, food wheel, restaurant choice, dinner decision, lunch picker">
    <meta name="author" content="SpinChoice">
    <meta property="og:title" content="What to Eat Decision Maker | SpinChoice Food Wheel">
    <meta property="og:description" content="Struggling with 'what to eat'? Let SpinChoice pick your meal! Pizza, Burgers, Salad, or Tacos - let the wheel decide!">
    <meta property="og:type" content="website">
    <meta property="og:url" content="">
    <meta property="og:image" content="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><circle cx='256' cy='256' r='240' fill='%234CAF50'/><text x='256' y='320' text-anchor='middle' fill='white' font-size='120' font-family='Arial'>🍕</text></svg>">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="What to Eat Decision Maker | SpinChoice">
    <meta name="twitter:description" content="Can't decide what to eat? Let SpinChoice pick your meal! Pizza, Burgers, Salad, or Tacos.">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4CAF50">
    <meta name="description" content="SpinChoice - A simple random decision maker PWA">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Apple Touch Icon -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SpinChoice">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='45' fill='%234CAF50'/><text x='50' y='58' text-anchor='middle' fill='white' font-size='24' font-family='Arial'>🎯</text></svg>">
    
    <!-- Styles -->
    <link rel="stylesheet" href="style.css">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "SpinChoice Food Picker",
      "description": "Food decision maker wheel for choosing what to eat - Pizza, Burgers, Salad, or Tacos",
      "applicationCategory": "Utility",
      "operatingSystem": "Any",
      "offers": {
        "@type": "Offer",
        "price": "0"
      },
      "featureList": [
        "Food decision making",
        "Meal planning assistance",
        "Restaurant choice helper",
        "Mobile-friendly design",
        "Offline functionality"
      ]
    }
    </script>
</head>
<body>
    <div class="container">
        <header>
            <h1 class="app-title">🍕 What to Eat?</h1>
            <p class="subtitle">Let the wheel pick your next meal</p>
            <nav class="page-nav">
                <a href="index.html" class="nav-link">All Options</a>
                <a href="yes-no.html" class="nav-link">Yes/No</a>
                <a href="random-choice.html" class="nav-link">Custom Wheel</a>
            </nav>
        </header>

        <main class="main-content">
            <!-- Preset Info -->
            <div class="preset-info">
                <h2 class="preset-title">🍽️ Food Decision Wheel</h2>
                <p class="preset-description">
                    Tired of the eternal "what should we eat?" question? Let SpinChoice decide! 
                    Choose from popular food options: Pizza, Burgers, Salad, and Tacos.
                </p>
            </div>

            <!-- Custom Options Input (Hidden for preset pages) -->
            <div class="input-section" style="display: none;">
                <label for="custom-options" class="input-label">
                    Enter your options (one per line or comma-separated):
                </label>
                <textarea 
                    id="custom-options" 
                    class="options-input" 
                    placeholder="Pizza, Burgers, Salad, Tacos"
                    rows="4"
                >Pizza, Burgers, Salad, Tacos</textarea>
                <p class="input-help">Leave blank to use default food options</p>
            </div>

            <!-- Spin Button -->
            <div class="spin-section">
                <button id="spin-btn" class="spin-button">
                    <span class="spin-text">🎲 SPIN FOR FOOD</span>
                </button>
            </div>

            <!-- Result Display -->
            <div id="result-section" class="result-section hidden">
                <div class="result-label">Time for some:</div>
                <div id="result-text" class="result-text"></div>
                <button id="share-btn" class="share-button">
                    <span class="share-icon">📤</span>
                    Share Result
                </button>
            </div>

            <!-- Options Preview -->
            <div class="options-preview">
                <p class="preview-label">Today's menu:</p>
                <div id="options-list" class="options-list food-options">
                    <span class="food-option">🍕 Pizza</span>
                    <span class="food-option">🍔 Burgers</span>
                    <span class="food-option">🥗 Salad</span>
                    <span class="food-option">🌮 Tacos</span>
                </div>
            </div>

            <!-- Food Tips -->
            <div class="usage-examples">
                <h3 class="examples-title">Great for:</h3>
                <ul class="examples-list">
                    <li>🤷 Group dinner decisions</li>
                    <li>🏠 Quick lunch at home</li>
                    <li>🚚 Choosing delivery food</li>
                    <li>📅 Weekly meal planning</li>
                    <li>🎉 Party food selection</li>
                </ul>
            </div>
        </main>

        <footer>
            <p class="footer-text">🎯 SpinChoice v1.0 - Make decisions easier</p>
            <!-- Install App Banner -->
            <div id="install-banner" class="install-banner hidden">
                <div class="install-content">
                    <span class="install-icon">📱</span>
                    <span class="install-text">Install SpinChoice for quick access!</span>
                    <button id="install-btn" class="install-button">Install</button>
                    <button id="dismiss-install" class="dismiss-button">×</button>
                </div>
            </div>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="spinner"></div>
    </div>

    <!-- Scripts -->
    <script src="app.js"></script>
    <script>
        // Set preset options for this page
        document.addEventListener('DOMContentLoaded', function() {
            if (window.spinChoiceApp) {
                window.spinChoiceApp.setPresetOptions(['Pizza', 'Burgers', 'Salad', 'Tacos']);
            }
        });
    </script>
</body>
</html>
